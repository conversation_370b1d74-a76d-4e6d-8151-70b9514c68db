import { generateText, generateObject } from 'ai';
import { google } from '@ai-sdk/google';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';

export interface AICredential {
  id: number;
  name: string;
  provider: string;
  apiKey: string;
}

export interface AIRequest {
  provider: string;
  model: string;
  prompt: string;
  systemPrompt?: string;
  maxTokens?: number;
  temperature?: number;
  outputFormat?: 'text' | 'json' | 'markdown';
  schema?: Record<string, any>;
}

export class AIService {
  private getModelInstance(provider: string, model: string, apiKey: string) {
    switch (provider.toLowerCase()) {
      case 'google':
        // Set the API key as environment variable for Google
        process.env.GOOGLE_GENERATIVE_AI_API_KEY = apiKey;
        return google(model.toLowerCase().replace(/\s+/g, '-'));
      case 'openrouter':
        // Create OpenRouter instance with custom configuration
        const openrouter = createOpenAI({
          apiKey,
          baseURL: 'https://openrouter.ai/api/v1'
        });
        return openrouter(model);
      default:
        throw new Error(`Unsupported AI provider: ${provider}`);
    }
  }

  private getModelName(provider: string, model: string): string {
    switch (provider.toLowerCase()) {
      case 'google':
        switch (model.toLowerCase()) {
          case 'gemini pro':
          case 'gemini pro (legacy)':
          case 'gemini 1.5 pro':
            return 'gemini-1.5-pro';
          case 'gemini flash':
          case 'gemini flash (legacy)':
          case 'gemini 1.5 flash':
            return 'gemini-1.5-flash';
          case 'gemini 2.5 pro preview':
          case 'gemini-2.5-pro-preview-05-06':
            return 'gemini-2.5-pro-preview-05-06';
          case 'gemini 2.0 flash':
          case 'gemini-2.0-flash':
            return 'gemini-2.0-flash';
          case 'gemini 2.0 flash lite':
          case 'gemini-2.0-flash-lite':
            return 'gemini-2.0-flash-lite';
          default:
            return 'gemini-1.5-pro';
        }
      case 'openrouter':
        switch (model.toLowerCase()) {
          case 'claude 3 opus':
            return 'anthropic/claude-3-opus';
          case 'claude 3 sonnet':
            return 'anthropic/claude-3-sonnet';
          case 'gpt-4':
            return 'openai/gpt-4';
          case 'gpt-3.5':
            return 'openai/gpt-3.5-turbo';
          default:
            return 'openai/gpt-4';
        }
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  async generateResponse(request: AIRequest, credential: AICredential): Promise<any> {
    try {
      // Validate credential
      if (!credential) {
        throw new Error('No credential provided');
      }

      if (!credential.apiKey) {
        throw new Error(`API key is missing for credential '${credential.name}' (ID: ${credential.id})`);
      }

      // Validate request
      if (!request.provider) {
        throw new Error('Provider is required');
      }

      if (!request.model) {
        throw new Error('Model is required');
      }

      console.log(`AI Service: Using credential '${credential.name}' (ID: ${credential.id}) for provider '${request.provider}'`);

      const modelName = this.getModelName(request.provider, request.model);
      const modelInstance = this.getModelInstance(request.provider, modelName, credential.apiKey);

      const baseConfig = {
        model: modelInstance,
        prompt: request.prompt,
        system: request.systemPrompt,
        maxTokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
      };

      if (request.outputFormat === 'json' && request.schema) {
        // Use generateObject for structured output
        const zodSchema = this.convertToZodSchema(request.schema);
        const result = await generateObject({
          ...baseConfig,
          schema: zodSchema,
        });

        return result.object;
      } else {
        // Use generateText for regular text output
        const result = await generateText(baseConfig);

        if (request.outputFormat === 'markdown') {
          return {
            text: result.text,
            format: 'markdown'
          };
        }

        return {
          text: result.text
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`AI Service Error:`, {
        error: errorMessage,
        provider: request.provider,
        model: request.model,
        credentialId: credential?.id,
        credentialName: credential?.name,
        hasApiKey: !!credential?.apiKey
      });
      throw new Error(`AI generation failed: ${errorMessage}`);
    }
  }

  private convertToZodSchema(schema: Record<string, any>): z.ZodSchema {
    // Simple schema conversion - can be enhanced for more complex schemas
    if (schema.type === 'object' && schema.properties) {
      const shape: Record<string, z.ZodSchema> = {};

      for (const [key, prop] of Object.entries(schema.properties as Record<string, any>)) {
        switch (prop.type) {
          case 'string':
            shape[key] = z.string();
            break;
          case 'number':
            shape[key] = z.number();
            break;
          case 'boolean':
            shape[key] = z.boolean();
            break;
          case 'array':
            shape[key] = z.array(z.any());
            break;
          default:
            shape[key] = z.any();
        }
      }

      return z.object(shape);
    }

    // Fallback to a flexible object schema
    return z.object({
      result: z.any(),
    });
  }
}

export const aiService = new AIService();
