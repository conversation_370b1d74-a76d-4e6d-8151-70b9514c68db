import React, { useState } from 'react';
import { Node } from 'reactflow';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useQuery } from '@tanstack/react-query';
import { Credential, InputSchema } from '@/types/workflow';
import { Key } from 'lucide-react';
import CodeEditor from '@/components/ui/code-editor';
import SchemaBuilder from '../SchemaBuilder';
import FormRenderer from '../FormRenderer';

interface NodeConfigModalProps {
  node: Node;
  onClose: () => void;
  onUpdate: (nodeId: string, data: any) => void;
}

const NodeConfigModal: React.FC<NodeConfigModalProps> = ({ node, onClose, onUpdate }) => {
  const { data: credentials } = useQuery<Credential[]>({
    queryKey: ['/api/credentials'],
  });

  const [formData, setFormData] = useState(() => {
    // Initialize form data based on node type and existing data
    switch(node.type) {
      case 'input':
        // Handle both new and legacy schema formats
        let inputSchema: InputSchema;
        if (node.data.schema && typeof node.data.schema === 'object' && 'fields' in node.data.schema) {
          // New schema format
          inputSchema = node.data.schema as InputSchema;
        } else {
          // Legacy schema format - convert to new format
          const legacySchema = node.data.schema || { "query": "string" };
          inputSchema = {
            version: "1.0",
            title: "Input Form",
            description: "Workflow input parameters",
            fields: Object.entries(legacySchema).map(([name, type], index) => ({
              id: `field_${index}`,
              name,
              label: name.charAt(0).toUpperCase() + name.slice(1),
              type: type as any,
              validation: { required: type === 'string' || type === 'number' }
            }))
          };
        }

        return {
          name: node.data.name || `Input Node`,
          description: node.data.description || 'Accepts input data for the workflow',
          schema: inputSchema,
          legacySchema: JSON.stringify(node.data.legacySchema || node.data.schema || { "query": "string" }, null, 2)
        };
      case 'prompt':
        return {
          name: node.data.name || `Prompt Node`,
          description: node.data.description || 'Processes text through an LLM prompt',
          prompt: node.data.prompt || '',
          provider: node.data.provider || 'google',
          model: node.data.model || 'Gemini Pro',
          credentialId: node.data.credentialId || '',
          temperature: node.data.temperature || 0.7,
          maxTokens: node.data.maxTokens || 1000,
          outputFormat: node.data.outputFormat || 'text'
        };
      case 'agent':
        return {
          name: node.data.name || `Agent Node`,
          description: node.data.description || 'Uses an LLM with a system role',
          systemPrompt: node.data.systemPrompt || '',
          provider: node.data.provider || 'openrouter',
          model: node.data.model || 'Claude 3 Opus',
          credentialId: node.data.credentialId || '',
          temperature: node.data.temperature || 0.7,
          maxTokens: node.data.maxTokens || 1000,
          outputFormat: node.data.outputFormat || 'text',
          schema: JSON.stringify(node.data.schema || {}, null, 2)
        };
      case 'api':
        return {
          name: node.data.name || `API Node`,
          description: node.data.description || 'Makes an HTTP request to an API endpoint',
          url: node.data.url || 'https://api.example.com/endpoint',
          method: node.data.method || 'POST',
          headers: JSON.stringify(node.data.headers || { "Content-Type": "application/json" }, null, 2),
          authType: node.data.authType || 'none',
          apiKeyHeader: node.data.apiKeyHeader || 'x-api-key',
          credentialId: node.data.credentialId || ''
        };
      case 'custom':
        return {
          name: node.data.name || `Custom Node`,
          description: node.data.description || 'Custom node with user-defined logic',
          code: node.data.code || '// Define your custom logic here\nfunction execute(input, context) {\n  // Process the input data\n  return {\n    result: "Custom processing complete",\n    data: input\n  };\n}',
          inputs: JSON.stringify(node.data.inputs || [{ name: 'input', type: 'any', required: true }], null, 2),
          outputs: JSON.stringify(node.data.outputs || [{ name: 'output', type: 'any' }], null, 2)
        };
      default:
        return {
          name: node.data.name || `Node`,
          description: node.data.description || ''
        };
    }
  });

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    let updatedData = { ...node.data };

    // Process the form data based on node type
    switch(node.type) {
      case 'input':
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description,
          schema: formData.schema,
          legacySchema: formData.legacySchema ? JSON.parse(formData.legacySchema) : undefined
        };
        break;

      case 'prompt':
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description,
          prompt: formData.prompt,
          provider: formData.provider,
          model: formData.model,
          credentialId: parseInt(formData.credentialId) || null,
          temperature: parseFloat(formData.temperature),
          maxTokens: parseInt(formData.maxTokens),
          outputFormat: formData.outputFormat
        };
        break;

      case 'agent':
        try {
          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            systemPrompt: formData.systemPrompt,
            provider: formData.provider,
            model: formData.model,
            credentialId: parseInt(formData.credentialId) || null,
            temperature: parseFloat(formData.temperature),
            maxTokens: parseInt(formData.maxTokens),
            outputFormat: formData.outputFormat,
            schema: formData.schema && typeof formData.schema === 'string' ? JSON.parse(formData.schema) : formData.schema
          };
        } catch (e) {
          alert('Invalid JSON schema format');
          return;
        }
        break;

      case 'api':
        try {
          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            url: formData.url,
            method: formData.method,
            headers: JSON.parse(formData.headers || '{}'),
            authType: formData.authType,
            apiKeyHeader: formData.apiKeyHeader,
            credentialId: parseInt(formData.credentialId) || null
          };
        } catch (e) {
          alert('Invalid JSON headers format');
          return;
        }
        break;

      case 'custom':
        try {
          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            code: formData.code,
            inputs: JSON.parse(formData.inputs || '[]'),
            outputs: JSON.parse(formData.outputs || '[]')
          };
        } catch (e) {
          alert('Invalid JSON format in inputs or outputs');
          return;
        }
        break;

      default:
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description
        };
    }

    onUpdate(node.id, updatedData);
    onClose();
  };

  const getTitle = () => {
    switch(node.type) {
      case 'input': return 'Configure Input Node';
      case 'prompt': return 'Configure Prompt Node';
      case 'agent': return 'Configure Agent Node';
      case 'api': return 'Configure API Node';
      case 'custom': return 'Configure Custom Node';
      default: return 'Configure Node';
    }
  };

  const getDescription = () => {
    switch(node.type) {
      case 'input': return 'Configure the input parameters and schema for this workflow entry point.';
      case 'prompt': return 'Set up the prompt template and AI model settings for text processing.';
      case 'agent': return 'Configure the AI agent with system prompts and behavior settings.';
      case 'api': return 'Configure API endpoint settings, authentication, and request parameters.';
      case 'custom': return 'Define custom logic, input parameters, and output structure for this node.';
      default: return 'Configure the settings and parameters for this workflow node.';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="node-name">Node Name</Label>
              <Input
                id="node-name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                className="mt-1"
              />
            </div>

            <div className="col-span-2">
              <Label htmlFor="node-description">Description</Label>
              <Textarea
                id="node-description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                className="mt-1"
                rows={2}
              />
            </div>

            {node.type === 'input' && (
              <div className="col-span-2">
                <Label>Input Schema Configuration</Label>
                <Tabs defaultValue="visual" className="mt-2">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="visual">Visual Builder</TabsTrigger>
                    <TabsTrigger value="preview">Form Preview</TabsTrigger>
                    <TabsTrigger value="legacy">Legacy JSON</TabsTrigger>
                  </TabsList>

                  <TabsContent value="visual" className="mt-4">
                    <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
                      <SchemaBuilder
                        schema={formData.schema as InputSchema}
                        onChange={(schema) => handleChange('schema', schema)}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="preview" className="mt-4">
                    <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
                      <FormRenderer
                        schema={formData.schema as InputSchema}
                        values={{}}
                        onChange={() => {}}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="legacy" className="mt-4">
                    <CodeEditor
                      value={formData.legacySchema || '{}'}
                      onChange={(value) => handleChange('legacySchema', value)}
                      language="json"
                      height="200px"
                    />
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                      Legacy JSON schema format (for backward compatibility)
                    </p>
                  </TabsContent>
                </Tabs>
              </div>
            )}

            {node.type === 'prompt' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-provider">LLM Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => handleChange('provider', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="google">Google</SelectItem>
                      <SelectItem value="openrouter">OpenRouter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-model">LLM Model</Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => handleChange('model', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {formData.provider === 'google' ? (
                        <>
                          <SelectItem value="Gemini Pro">Gemini Pro</SelectItem>
                          <SelectItem value="Gemini Flash">Gemini Flash</SelectItem>
                        </>
                      ) : (
                        <>
                          <SelectItem value="Claude 3 Opus">Claude 3 Opus</SelectItem>
                          <SelectItem value="Claude 3 Sonnet">Claude 3 Sonnet</SelectItem>
                          <SelectItem value="GPT-4">GPT-4</SelectItem>
                          <SelectItem value="GPT-3.5">GPT-3.5</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-credential">API Credential</Label>
                  <Select
                    value={formData.credentialId.toString()}
                    onValueChange={(value) => handleChange('credentialId', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select credential" />
                    </SelectTrigger>
                    <SelectContent>
                      {credentials?.map(cred => (
                        <SelectItem key={cred.id} value={cred.id.toString()}>
                          {cred.name} ({cred.provider})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-prompt">Prompt Template</Label>
                  <Textarea
                    id="node-prompt"
                    value={formData.prompt}
                    onChange={(e) => handleChange('prompt', e.target.value)}
                    className="mt-1 font-mono"
                    rows={5}
                    placeholder="Create a detailed outline for an article about: {{query}}"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Use double curly braces for variables: {`{{variable_name}}`}
                  </p>
                </div>

                <div className="col-span-1">
                  <Label>Temperature</Label>
                  <div className="flex items-center mt-2">
                    <Slider
                      min={0}
                      max={1}
                      step={0.1}
                      value={[parseFloat(formData.temperature)]}
                      onValueChange={([value]) => handleChange('temperature', value)}
                      className="w-full"
                    />
                    <span className="ml-2 text-sm">{formData.temperature}</span>
                  </div>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-max-tokens">Max Tokens</Label>
                  <Input
                    id="node-max-tokens"
                    type="number"
                    value={formData.maxTokens}
                    onChange={(e) => handleChange('maxTokens', e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div className="col-span-2">
                  <Label>Output Format</Label>
                  <RadioGroup
                    value={formData.outputFormat}
                    onValueChange={(value) => handleChange('outputFormat', value)}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="text" id="text-output" />
                      <Label htmlFor="text-output">Text</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="json" id="json-output" />
                      <Label htmlFor="json-output">JSON</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="markdown" id="markdown-output" />
                      <Label htmlFor="markdown-output">Markdown</Label>
                    </div>
                  </RadioGroup>
                </div>
              </>
            )}

            {node.type === 'agent' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-provider">LLM Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => handleChange('provider', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="google">Google</SelectItem>
                      <SelectItem value="openrouter">OpenRouter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-model">LLM Model</Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => handleChange('model', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {formData.provider === 'google' ? (
                        <>
                          <SelectItem value="Gemini Pro">Gemini Pro</SelectItem>
                        </>
                      ) : (
                        <>
                          <SelectItem value="Claude 3 Opus">Claude 3 Opus</SelectItem>
                          <SelectItem value="Claude 3 Sonnet">Claude 3 Sonnet</SelectItem>
                          <SelectItem value="GPT-4">GPT-4</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-credential">API Credential</Label>
                  <Select
                    value={formData.credentialId.toString()}
                    onValueChange={(value) => handleChange('credentialId', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select credential" />
                    </SelectTrigger>
                    <SelectContent>
                      {credentials?.map(cred => (
                        <SelectItem key={cred.id} value={cred.id.toString()}>
                          {cred.name} ({cred.provider})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-system-prompt">System Prompt</Label>
                  <Textarea
                    id="node-system-prompt"
                    value={formData.systemPrompt}
                    onChange={(e) => handleChange('systemPrompt', e.target.value)}
                    className="mt-1 font-mono"
                    rows={5}
                    placeholder="You are a professional content writer. Create a well-structured article based on this outline."
                  />
                </div>

                <div className="col-span-1">
                  <Label>Temperature</Label>
                  <div className="flex items-center mt-2">
                    <Slider
                      min={0}
                      max={1}
                      step={0.1}
                      value={[parseFloat(formData.temperature)]}
                      onValueChange={([value]) => handleChange('temperature', value)}
                      className="w-full"
                    />
                    <span className="ml-2 text-sm">{formData.temperature}</span>
                  </div>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-max-tokens">Max Tokens</Label>
                  <Input
                    id="node-max-tokens"
                    type="number"
                    value={formData.maxTokens}
                    onChange={(e) => handleChange('maxTokens', e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div className="col-span-2">
                  <Label>Output Format</Label>
                  <RadioGroup
                    value={formData.outputFormat}
                    onValueChange={(value) => handleChange('outputFormat', value)}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="text" id="text-output" />
                      <Label htmlFor="text-output">Text</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="json" id="json-output" />
                      <Label htmlFor="json-output">JSON</Label>
                    </div>
                  </RadioGroup>
                </div>

                {formData.outputFormat === 'json' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-schema">JSON Schema (optional)</Label>
                    <CodeEditor
                      value={typeof formData.schema === 'string' ? formData.schema : '{}'}
                      onChange={(value) => handleChange('schema', value)}
                      language="json"
                      height="150px"
                      className="mt-1"
                    />
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                      Define the expected structure of the JSON output
                    </p>
                  </div>
                )}
              </>
            )}

            {node.type === 'api' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-url">API Endpoint</Label>
                  <Input
                    id="node-url"
                    value={formData.url}
                    onChange={(e) => handleChange('url', e.target.value)}
                    className="mt-1"
                    placeholder="https://api.example.com/endpoint"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-method">HTTP Method</Label>
                  <Select
                    value={formData.method}
                    onValueChange={(value) => handleChange('method', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GET">GET</SelectItem>
                      <SelectItem value="POST">POST</SelectItem>
                      <SelectItem value="PUT">PUT</SelectItem>
                      <SelectItem value="DELETE">DELETE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-headers">Headers (JSON)</Label>
                  <CodeEditor
                    value={formData.headers || '{}'}
                    onChange={(value) => handleChange('headers', value)}
                    language="json"
                    height="100px"
                    className="mt-1"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-auth-type">Authentication</Label>
                  <Select
                    value={formData.authType}
                    onValueChange={(value) => handleChange('authType', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select auth type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="apiKey">API Key</SelectItem>
                      <SelectItem value="bearer">Bearer Token</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.authType === 'apiKey' && (
                  <div className="col-span-1">
                    <Label htmlFor="node-api-key-header">API Key Header</Label>
                    <Input
                      id="node-api-key-header"
                      value={formData.apiKeyHeader}
                      onChange={(e) => handleChange('apiKeyHeader', e.target.value)}
                      className="mt-1"
                      placeholder="x-api-key"
                    />
                  </div>
                )}

                {formData.authType !== 'none' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-credential">Credential</Label>
                    <Select
                      value={formData.credentialId.toString()}
                      onValueChange={(value) => handleChange('credentialId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select credential" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None</SelectItem>
                        {credentials?.map(cred => (
                          <SelectItem key={cred.id} value={cred.id.toString()}>
                            {cred.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </>
            )}

            {node.type === 'custom' && (
              <>
                <div className="col-span-2">
                  <Label htmlFor="node-code">Custom Logic</Label>
                  <CodeEditor
                    value={formData.code || ''}
                    onChange={(value) => handleChange('code', value)}
                    language="javascript"
                    height="300px"
                    className="mt-1"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Define your custom node logic. The function should accept (input, context) and return the output.
                  </p>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-inputs">Input Parameters (JSON)</Label>
                  <CodeEditor
                    value={formData.inputs || '[]'}
                    onChange={(value) => handleChange('inputs', value)}
                    language="json"
                    height="150px"
                    className="mt-1"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Define input parameters: {`[{"name": "param", "type": "string", "required": true}]`}
                  </p>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-outputs">Output Parameters (JSON)</Label>
                  <CodeEditor
                    value={formData.outputs || '[]'}
                    onChange={(value) => handleChange('outputs', value)}
                    language="json"
                    height="150px"
                    className="mt-1"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Define output parameters: {`[{"name": "result", "type": "string"}]`}
                  </p>
                </div>
              </>
            )}

            {formData.credentialId && (
              <div className="col-span-2 pt-2">
                <div className="flex items-center text-neutral-600 dark:text-neutral-400 text-sm">
                  <Key className="w-4 h-4 mr-2 text-primary" />
                  <span>Using credential: <strong>
                    {credentials?.find(c => c.id === parseInt(formData.credentialId))?.name || 'Unknown'}
                  </strong></span>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NodeConfigModal;
